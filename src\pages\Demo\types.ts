// 消息类型
export type MessageItem = {
  id: string;
  step: number; //步骤
  title: React.ReactNode; //任务名称
  spendTime?: number; //耗时多少秒
  token?: number; //消耗token
  subTitle?: string; //子任务名称

  description?: string; //描述(json 字符串)
  state?: number; // 任务状态,1 表示完成
};

export type TaskItem = {
  key: string;
  name: string;
  description?: any;
  spendTime?: number;
  spendToken?: number;
  state?: number;
  token?: number;
  task1has?: number;
  task1total?: number;
  task2has?: number;
  task2total?: number;
  task3has?: number;
  task3total?: number;
  task3success?: number;
  task3fail?: number;
  generateNum?: number;
  total?: number;
  [key: string]: any;
};

export type StepItem = {
  step: number;
  stepName: string;
  stepTitle: string;
  stepSpendTime?: number;
  stepSpendToken?: number;
  taskList: TaskItem[];
};

export type TimeLineItem = {
  id: string;
  step: number; //步骤
  content?: React.ReactNode;
  typing?: boolean; //是否打字效果
  avatar?: any; // 是否有头像
  spendTime?: number;
  token?: number;
  state: number;
  description?: any;
  [key: string]: any;
};

export type DetailItemType = {
  id: string;
  title: React.ReactNode;
  body: React.ReactNode;
  type?: number;
};
