import http from "../utils/request";

type RequestOptions = {
  requestProps?: Record<string, any>;
  apiVersion?: string;
};
export function apiTest1(options?: RequestOptions): Promise<any> {
  const { requestProps = {}, apiVersion = "v1" } = options || {};
  return http.request({
    url: `/api/progress/triggerDemoJsonPush`,
    method: "get",
    params: { limit: 5 },
    data: {},
    ...requestProps,
  });
}
export function apiTest(options?: RequestOptions): Promise<any> {
  const { requestProps = {}, apiVersion = "v1" } = options || {};
  return http.request({
    url: `/api/progress/run`,
    method: "get",
    params: { limit: 5 },
    data: {},
    ...requestProps,
  });
}
/*
1. API Retrieval，获取接口， 对应 python 1,2 阶段。

2.API Categorization，接口分类   对应 python 3，4 阶段

3.Call Chain Generation，生成调用链  对应 python 8 阶段

4.Vulnerability Detection，漏洞检测     对应 python 8 阶段

5.POC Generation，poc 生成

6.POC Verification，poc 验证
*/
export function apiTest2(stage: number, options?: RequestOptions): Promise<any> {
  const { requestProps = {}, apiVersion = "v1" } = options || {};
  return http.request({
    url: `/api/progress/run-test`,
    method: "get",
    params: {
      stage: {
        1: "API Retrieval",
        2: "API Categorization",
        3: "Call Chain Generation",
        4: "Vulnerability Detection",
        5: "POC Generation",
        6: "POC Verification",
      }[stage],
    },
    data: {},
    ...requestProps,
  });
}
// /api/progress/run

export function apiLoop({ id, step }: { id: string; step: number }, options?: RequestOptions): Promise<any> {
  const { requestProps = {} } = options || {};
  return http.request({
    url: `/api/progress/run`,
    method: "post",
    params: {},
    data: { id, step },
    ...requestProps,
  });
}

// /api/progress/runPythonScript
export function apiStart(options?: RequestOptions): Promise<any> {
  const { requestProps = {} } = options || {};
  return http.request({
    url: `/api/progress/runPythonScript`,
    method: "get",
    params: {},
    data: {},
    ...requestProps,
  });
}

export function apiExists(options?: RequestOptions): Promise<any> {
  const { requestProps = {} } = options || {};
  return http.request({
    url: `/api/progress/exists`,
    method: "get",
    params: {},
    data: {},
    ...requestProps,
  });
}

// http://localhost:8062/api/progress/upload

import type { AxiosProgressEvent } from "axios";

export function apiUpload(
  file?: File,
  options?: RequestOptions & { onProgress?: (percent: number) => void },
): Promise<any> {
  const { requestProps = {}, onProgress } = options || {};
  const formData = new FormData();
  formData.append("startDate", String(Date.now()));
  if (file) formData.append("file", file);
  return http.request({
    url: `/api/progress/upload`,
    method: "post",
    params: {},
    data: formData,
    // 兼容 axios 实现的 http.request
    onUploadProgress: (progressEvent: AxiosProgressEvent) => {
      if (progressEvent.total && typeof onProgress === "function") {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percent);
      }
    },
    ...requestProps,
  });
}
