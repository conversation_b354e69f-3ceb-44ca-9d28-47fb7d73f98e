// import { ChromeFilled, <PERSON>Filled, <PERSON>Filled, TabletFilled } from "@ant-design/icons";
import { ProLayout } from "@ant-design/pro-components";
import { Outlet } from "react-router";
import { useSelector } from "react-redux";
// import { useEffect } from "react";
import { useLocation } from "react-router";
import { useNavigate } from "react-router";
import { Avatar, Flex, theme } from "antd";
const { useToken } = theme;

// 根布局组件
export function Layout() {
  const location = useLocation();
  const navigate = useNavigate();
  const pages: any = useSelector((state: any) => state.pages.list);

  const route = {
    path: "/",
    routes: pages.map((el: any) => {
      return {
        path: el.path,
        name: el.name,
        icon: el.icon,
        component: el.component,
        routes: el.children,
      };
    }),
  };
  return (
    <ProLayout
      layout="mix"
      title="检测机构端应用系统"
      logo={false}
      location={{
        pathname: location.pathname,
      }}
      route={route}
      menuItemRender={(item, dom) => <div onClick={() => navigate({ pathname: item.path })}>{dom}</div>}
      avatarProps={{
        size: "small",
        render: () => {
          return (
            <Flex align="center" gap={8}>
              <Avatar />
              管理员
            </Flex>
          );
        },
      }}
      token={{
        sider: {
          colorMenuBackground: "#fff",
          colorBgMenuItemHover: "#f6f6f6",
          colorTextMenu: "#595959",
          colorTextMenuSelected: "#242424",
          colorTextMenuActive: "#242424",
        },
      }}
    >
      <Outlet />
    </ProLayout>
  );
}
