import { Prism as Syntax<PERSON><PERSON>light<PERSON> } from "react-syntax-highlighter";
import MdBlock from "./MdBlock";
// import { docco } from "react-syntax-highlighter/dist/esm/styles/hljs";
// import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Typography } from "antd";

export default function CodeBlock({ text, language }: { text: string; language?: string }) {
  if (language === "md") {
    return (
      <Typography>
        <MdBlock code={text} />
      </Typography>
    );
  }

  return (
    <Typography>
      <SyntaxHighlighter
        // style={dark}
        customStyle={{
          whiteSpace: "pre-wrap",
          wordBreak: "break-word" /* 对HTML更友好的换行 */,
        }}
        language={language || "java"}
        wrapLines
        wrapLongLines={true}
      >
        {text}
      </SyntaxHighlighter>
    </Typography>
  );
}
