import type { CSSProperties } from "react";

// 容器样式（蓝色边框）
export const containerStyle: CSSProperties = {
  height: "100vh",
  display: "flex",
  flexDirection: "column",
  backgroundColor: "#fff",
  // maxWidth: 1400,
  margin: "auto",
};

// 列公共样式
export const columnStyle: CSSProperties = {
  flex: 1,
  overflowY: "auto",
};

// 左列样式（浅蓝背景）
export const leftColumnStyle: CSSProperties = {
  ...columnStyle,
  display: "flex",
  flexDirection: "column",
  position: "relative", // 为固定 footer 做准备
  backgroundColor: "rgba(0,0,0,0.02)",
};

export const rightColumnStyle: CSSProperties = {
  ...columnStyle,
};

export const fixedFooterStyle: CSSProperties = {
  padding: "20px",
  paddingTop: 0,
  textAlign: "center",
  position: "sticky",
  bottom: 0,
  zIndex: 1,
  backgroundColor: "#fff",
};
