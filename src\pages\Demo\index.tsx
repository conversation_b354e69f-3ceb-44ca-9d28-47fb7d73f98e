import { BugOutlined, CheckCircleOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, Collapse, Divider, Empty, Flex, Layout, Space, Spin, Tag, Typography, theme } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { containerStyle, fixedFooterStyle, leftColumnStyle, rightColumnStyle } from "./css";
import "./index.css";
import type { DetailItemType, TaskItem, TimeLineItem } from "./types";
// import { UUID } from "../../utils/uuid";
import { getTimeLine } from "./timeLine";
// import DataContent from "./DataContent";‚
import { AlertOutlined, CoffeeOutlined, RobotFilled } from "@ant-design/icons";
import { Avatar, Card } from "antd";
// import { Prompts } from "@ant-design/x";
import { apiExists } from "../../apis/file";
import { formatSeconds } from "../../utils/date";
import { scrollToBottom } from "../../utils/scrollToBottom";
import MyBubble from "./MyBubble";

//
const { Content, Header } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;
const { useToken } = theme;
const headPadding = "10px 0 15px 0";
let timer: any;
const Demo = () => {
  const fileRef = useRef<HTMLInputElement>(null);
  const leftColumnRef = useRef<HTMLDivElement>(null);
  const rightColumnRef = useRef<HTMLDivElement>(null);
  const [detail, setDetail] = useState<DetailItemType[]>([]);
  const [messageList, setMessageList] = useState<TimeLineItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { token } = useToken();
  const [fileDomKey, setFileDomKey] = useState(1);
  const [activeDetailKey, setActiveDetailKey] = useState<any>("1-1");
  // const [exists, setExists] = useState(false);
  const exists = useRef(false);
  const [isScrollLeft, setIsScrollLeft] = useState(true);
  const [isScrollRight, setIsScrollRight] = useState(true);

  function updateMessageList(data: TimeLineItem) {
    setMessageList((prev) => {
      if (!prev.find((el) => el.id === data.id)) {
        return [...prev, data];
      }
      return prev.map((item) => (item.id === data.id ? data : item));
    });
  }

  const player = useCallback(async (file?: File) => {
    setLoading(true);
    const list = getTimeLine({ file, setDetail, setActiveDetailKey, exists: exists.current });
    // list.length = 18;
    // 轮询请求，直到 data.state === 1，每次间隔 1 秒
    async function pollUntilStateOne({ request }: { request: () => Promise<TimeLineItem> }) {
      let data;
      // 一直请求，直到 data.state === 1，再跳出
      while (true) {
        try {
          data = await request();
          updateMessageList(data);
          if (data.state == 1) {
            break;
          }
        } catch (error) {
          console.log(error);
        }

        await new Promise((resolve) => {
          timer = setTimeout(resolve, 2000);
        });
      }
    }
    for (const item of list) {
      await pollUntilStateOne({ request: item.request });
    }
    setLoading(false);
  }, []);

  useEffect(() => {
    apiExists().then((res) => {
      exists.current = true;
      if (exists.current) {
        // setIsScrollLeft(false);
        // setIsScrollRight(false);
        // console.log(233);
        player();
      }
    });

    return () => {
      clearTimeout(timer);
    };
  }, [player]);

  const dataResult = useMemo<Record<string, TaskItem>>(() => {
    // console.log(33, messageList);
    const data = {};
    messageList.forEach((el) => {
      data[el.id] = el;
    });
    return data;
  }, [messageList]);

  const lastId = useMemo(() => {
    return messageList.length > 0 ? messageList[messageList.length - 1].id : "";
  }, [messageList]);

  const lastDetailId = useMemo(() => {
    return detail.length > 0 ? detail[detail.length - 1].id : "";
  }, [detail]);

  useEffect(() => {
    // console.log(3245543, lastDetailId);
    setActiveDetailKey(lastDetailId);
  }, [lastDetailId]);

  useEffect(() => {
    console.log("dataResult:", dataResult);
  }, [dataResult]);

  // 更新后滚动到底部
  useEffect(() => {
    if (isScrollLeft) {
      scrollToBottom(leftColumnRef.current as HTMLDivElement);
    }
  }, [lastId, isScrollLeft]);

  // 更新后滚动到底部
  useEffect(() => {
    if (isScrollRight) {
      scrollToBottom(rightColumnRef.current as HTMLDivElement);
    }
  }, [lastDetailId, isScrollRight]);

  const total = useMemo<{ spendTime: number; token: number }>(() => {
    let spendTime = 0;
    let token = 0;
    messageList.forEach((el) => {
      if (el.spendTime) {
        spendTime += Number(el.spendTime);
      }
      if (el.token) {
        token += Number(el.token);
      }
    });
    return { spendTime, token };
  }, [messageList]);

  const des3_3 = useMemo(() => dataResult["3-3"]?.description ?? {}, [dataResult]);
  const des4_1 = useMemo(() => dataResult["4-1"]?.description ?? {}, [dataResult]);

  return (
    <div style={containerStyle}>
      {/* 主内容区 */}
      <Header
        style={{
          display: "flex",
          backgroundColor: token.colorBgContainer,
          borderBottom: `1px solid ${token.colorBorderSecondary}`,
        }}
      >
        <Space>
          <BugOutlined style={{ color: "blue" }} />
          <div style={{ fontWeight: "bold" }}>大模型漏洞挖掘平台</div>
        </Space>
      </Header>
      <Content style={{ display: "flex", flex: 1 }}>
        {/* 左列（带滚动和固定 footer） */}
        <div style={{ ...leftColumnStyle, overflowY: "auto" }} ref={leftColumnRef} className="custom-scrollbar">
          <div style={{ flex: 1, padding: "20px 10px" }}>
            <>
              {/* 1 */}
              {dataResult["1"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>导入源代码文件</MyBubble>
                </Space>
              )}
              {dataResult["1-0"] && (
                <Card
                  title={
                    <Space>
                      {/* <CoffeeOutlined style={{ color: "blue" }} /> */}
                      <MyBubble>上传文件</MyBubble>
                      {dataResult["1-1"]?.spendTime && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["1-1"]?.spendTime)}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space>
                    {dataResult["1-1"]?.state === 1 ? (
                      <>
                        <CheckCircleOutlined style={{ color: "green" }} />
                        <a href="#1">上传成功</a>
                      </>
                    ) : (
                      <>
                        <Spin size="small" />
                        <MyBubble>文件上传中</MyBubble>
                      </>
                    )}
                  </Space>
                </Card>
              )}

              {/* 2 */}
              {dataResult["2"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>代码预处理</MyBubble>
                </Space>
              )}

              {dataResult["2-0"] && (
                <Card
                  title={
                    <Space>
                      {/* <CoffeeOutlined style={{ color: "blue" }} /> */}
                      <MyBubble>对源码进行静态分析，生成抽象语法树</MyBubble>
                      {dataResult["2-1"]?.spendTime && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["2-1"]?.spendTime)}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical">
                    {dataResult["2-1"]?.state === 1 ? (
                      <Space>
                        <CheckCircleOutlined style={{ color: "green" }} />
                        处理完成
                      </Space>
                    ) : (
                      <Space>
                        <Spin size="small" />
                        处理中
                      </Space>
                    )}
                  </Space>
                </Card>
              )}
              {/* 3 */}
              {dataResult["3"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>API接口梳理</MyBubble>
                </Space>
              )}
              {dataResult["3-0"] && (
                <Card
                  title={
                    <Space>
                      <MyBubble>梳理函数调用接口</MyBubble>
                      {dataResult["3-3"]?.state === 1 && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["3-3"]?.spendTime || 0)}
                        </Tag>
                      )}
                      {dataResult["3-3"]?.state === 1 && (
                        <Tag bordered color="blue">
                          消耗token {dataResult["3-2"]?.token}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {dataResult["3-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务一：梳理函数调用接口</MyBubble>
                          </Space>
                        }
                      >
                        <Space>
                          {dataResult["3-1"]?.state == 1 ? (
                            <>
                              <CheckCircleOutlined style={{ color: "green" }} />
                              处理完成
                            </>
                          ) : (
                            <>
                              <Spin size="small" />
                              处理中
                            </>
                          )}

                          <a href="#3-1" onClick={() => setActiveDetailKey("3-1")}>
                            共计找到接口{dataResult["3-1"]?.description?.length}个
                          </a>
                        </Space>
                      </Card>
                    )}
                    {dataResult["3-2"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务二：接口分类</MyBubble>
                          </Space>
                        }
                      >
                        <Space direction="vertical">
                          <Space align="start">
                            <AlertOutlined style={{ color: "blue" }} />
                            <Text type="secondary">
                              请将查询查询到的接口按污点源（Source）、传播点（Trans）、污点汇聚点（Sink）类型进行分类，并按照该数据格式进行返回
                            </Text>
                          </Space>
                          <Space>
                            {dataResult["3-2"]?.state ? (
                              <>
                                <CheckCircleOutlined style={{ color: "green" }} />
                                分类完成
                              </>
                            ) : (
                              <>
                                <Spin size="small" />
                                处理中
                              </>
                            )}

                            <a href="#3-2" onClick={() => setActiveDetailKey("3-2")}>
                              污点源（Source）<>{dataResult["3-2"]?.sourceCount}</>
                              个，传播点（Trans）<>{dataResult["3-2"].transCount}</>
                              个，污点汇聚点（Sink）<>{dataResult["3-2"]?.sinkCount}</>个。
                            </a>
                          </Space>
                        </Space>
                      </Card>
                    )}
                    {dataResult["3-3"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            任务三：分析污点源（Source）、传播点（Trans）、污点汇聚点（Sink）调用过程，识别调用链
                          </Space>
                        }
                      >
                        <Space align="start">
                          {dataResult["3-3"]?.state ? (
                            <>
                              <CheckCircleOutlined style={{ color: "green" }} />
                              <div style={{ minWidth: "60px" }}>查找完成</div>
                            </>
                          ) : (
                            <>
                              <Spin size="small" />
                              <div style={{ minWidth: "60px" }}>处理中</div>
                            </>
                          )}
                          <div>
                            <a href="#3-3">
                              找到调用成功的调用链共
                              {Object.entries(des3_3).reduce((pre, cur: any) => pre + (cur?.[1]?.length ?? 0), 0)}个。
                            </a>
                            其中
                            {Object.entries(des3_3).map(([key, value]: any, i: number) => (
                              <>
                                <a href={`#3-3-${key}`} key={key}>
                                  {key} {value.length} 个
                                </a>
                                {Object.entries(des3_3).length - 1 === i ? "" : "，"}
                              </>
                            ))}
                            。
                          </div>
                        </Space>
                      </Card>
                    )}
                  </Space>
                </Card>
              )}
              {/* 4 */}
              {dataResult["4"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>漏洞检测</MyBubble>
                </Space>
              )}
              {dataResult["4-0"] && (
                <Card
                  title={
                    <Space>
                      <MyBubble>漏洞检测</MyBubble>
                      {dataResult["4-1"]?.spendTime && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["4-1"]?.spendTime)}
                        </Tag>
                      )}
                      {dataResult["4-1"]?.token && (
                        <Tag bordered color="blue">
                          消耗token {dataResult["4-1"]?.token}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {dataResult["4-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务一：RCE命令注入漏洞检测</MyBubble>
                          </Space>
                        }
                      >
                        <Space direction="vertical">
                          <Space align="start">
                            <AlertOutlined style={{ color: "blue" }} />
                            <Text type="secondary">
                              你是一名安全专家。分析这个 Java
                              项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。
                            </Text>
                          </Space>
                          <Space align="start">
                            {dataResult["4-1"]?.state ? (
                              <>
                                <CheckCircleOutlined style={{ color: "green" }} />
                                <div style={{ minWidth: "60px" }}> 处理完成 </div>
                              </>
                            ) : (
                              <>
                                <Spin size="small" />
                                <div style={{ minWidth: "60px" }}> 处理中 </div>
                              </>
                            )}

                            <div>
                              <a href="#4-1" onClick={() => setActiveDetailKey("4-1")}>
                                共计发现{Object.keys(des4_1).reduce((pre, cur) => pre + des4_1[cur].length, 0)}个漏洞，
                              </a>
                              其中
                              {Object.entries(des4_1).map(([key, value]: any, i: number) => (
                                <>
                                  <a href={`#4-1-${key}`} key={key}>
                                    {key} {value.length} 个
                                  </a>
                                  {Object.entries(des4_1).length - 1 === i ? "。" : "，"}
                                </>
                              ))}
                            </div>
                          </Space>
                        </Space>
                      </Card>
                    )}
                  </Space>
                </Card>
              )}
              {/* 5 */}
              {dataResult["5"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>漏洞验证</MyBubble>
                </Space>
              )}
              {dataResult["5-0"] && (
                <Card
                  title={
                    <Space>
                      <MyBubble>漏洞验证</MyBubble>
                      {dataResult["5-1"]?.spendTime && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["5-1"]?.spendTime)}
                        </Tag>
                      )}
                      {dataResult["5-1"]?.token && (
                        <Tag bordered color="blue">
                          消耗token {dataResult["5-1"]?.token}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {dataResult["5-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务一：生成RCE命令注入POC脚本</MyBubble>({dataResult["5-1"]?.task1has}/
                            {dataResult["5-1"]?.task1total})
                          </Space>
                        }
                      >
                        <Space direction="vertical">
                          <Space align="start">
                            <AlertOutlined style={{ color: "blue" }} />
                            <Text type="secondary">
                              你是一个专门研究RCE漏洞的验证和利用的研究员，你精通 Nuclei
                              漏洞验证框架。你要根据下面的漏洞描述，
                              生成nuclei可用的poc，需要严格符合nuclei的模板规范，符合 Nuclei v3 语法规范，通过 nuclei
                              -validate 语法验证， 我将基于此模板进行漏洞验证。 Java
                              项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。
                            </Text>
                          </Space>
                          <Space>
                            {dataResult["5-1"].task1has === dataResult["5-1"].task1total ? (
                              <Space>
                                <CheckCircleOutlined style={{ color: "green" }} />
                                处理完成
                              </Space>
                            ) : (
                              <Space>
                                <Spin size="small" />
                                处理中
                              </Space>
                            )}

                            <a href="#5-1" onClick={() => setActiveDetailKey("5-1")}>
                              共计生成{dataResult["5-1"]?.task1has}个POC
                            </a>
                          </Space>
                        </Space>
                      </Card>
                    )}
                    {/* 5-2 */}
                    {dataResult["5-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务二：生成SQL注入POC脚本</MyBubble>({dataResult["5-1"]?.task2has}/
                            {dataResult["5-1"]?.task2total})
                          </Space>
                        }
                      >
                        <Space direction="vertical">
                          <Space align="start">
                            <AlertOutlined style={{ color: "blue" }} />
                            <Text type="secondary">
                              你是一个专门研究SQL注入漏洞的验证和利用的研究员，你精通 Nuclei
                              漏洞验证框架。你要根据下面的漏洞描述，生成nuclei可用的poc。
                            </Text>
                          </Space>
                          <Space>
                            {dataResult["5-1"]?.task2has === dataResult["5-1"]?.task2total ? (
                              <>
                                <CheckCircleOutlined style={{ color: "green" }} />
                                处理完成
                              </>
                            ) : (
                              <>
                                <Spin size="small" />
                                处理中
                              </>
                            )}

                            <a href="#5-2" onClick={() => setActiveDetailKey("5-2")}>
                              共计生成{dataResult["5-1"]?.task2has}个POC
                            </a>
                          </Space>
                        </Space>
                      </Card>
                    )}
                    {/* 5-3 */}
                    {dataResult["5-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务三：根据靶机利用POC动态验证漏洞</MyBubble>({dataResult["5-1"]?.task3has}/
                            {dataResult["5-1"]?.task3total})
                          </Space>
                        }
                      >
                        <Space>
                          {dataResult["5-1"]?.task3has === dataResult["5-1"]?.task3total ? (
                            <>
                              <CheckCircleOutlined style={{ color: "green" }} />
                              验证完成
                            </>
                          ) : (
                            <>
                              <Spin size="small" />
                              验证中
                            </>
                          )}

                          <a href="#5-3" onClick={() => setActiveDetailKey("5-3")}>
                            成功{dataResult["5-1"]?.task3success}个，失败{dataResult["5-1"]?.task3fail}个
                          </a>
                        </Space>
                      </Card>
                    )}
                  </Space>
                </Card>
              )}
              {/* 6 */}
              {dataResult["6"] && (
                <Space style={{ padding: headPadding }}>
                  <Avatar icon={<RobotFilled />} />
                  <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>修复建议</MyBubble>
                </Space>
              )}
              {dataResult["6-0"] && (
                <Card
                  title={
                    <Space>
                      <MyBubble>修复建议</MyBubble>
                      {dataResult["6-1"]?.state === 1 && (
                        <Tag bordered color="green">
                          用时 {formatSeconds(dataResult["6-1"]?.spendTime || 0)}
                        </Tag>
                      )}
                      {dataResult["6-1"]?.token && (
                        <Tag bordered color="blue">
                          消耗token {dataResult["6-1"]?.token}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {dataResult["6-1"] && (
                      <Card
                        type="inner"
                        title={
                          <Space>
                            <CoffeeOutlined style={{ color: "blue" }} />
                            <MyBubble>任务一：生成修复建议</MyBubble>({dataResult["6-1"]?.generateNum}/
                            {dataResult["6-1"]?.total})
                          </Space>
                        }
                      >
                        <Space direction="vertical">
                          <Space align="start">
                            <AlertOutlined style={{ color: "blue" }} />
                            <Text type="secondary">
                              角色： 代码审计专家，擅长发现并修复代码中的安全漏洞。 背景信息：
                              接收到一段存在安全漏洞的代码，需要对其进行详细分析并提出修复建议。 工作流程/工作任务：
                              1.仔细阅读并分析提供的代码，识别出其中的安全漏洞。2.
                              针对每个识别出的漏洞，提出具体的修复建议。3.
                              编写一份详细的审计报告，包括漏洞描述、修复建议和预防措施。
                            </Text>
                          </Space>
                          <Space>
                            {dataResult["6-1"].generateNum === dataResult["6-1"].total ? (
                              <>
                                <CheckCircleOutlined style={{ color: "green" }} />
                                生成完成
                              </>
                            ) : (
                              <>
                                <Spin size="small" />
                                生成中
                              </>
                            )}

                            <a href="#6-1" onClick={() => setActiveDetailKey("6-1")}>
                              修复建议
                            </a>
                          </Space>
                        </Space>
                      </Card>
                    )}
                  </Space>
                </Card>
              )}
              {dataResult["7"] && (
                <>
                  <Space style={{ padding: headPadding }}>
                    <Avatar icon={<RobotFilled />} />
                    <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>完成</MyBubble>
                  </Space>
                  <Card>
                    <Tag bordered color="green">
                      总用时 {formatSeconds(total.spendTime)}
                    </Tag>
                    <Tag bordered color="blue">
                      总消耗token {total.token}
                    </Tag>
                  </Card>
                </>
              )}
            </>
            {messageList.length === 0 && <Empty style={{ marginTop: "150px" }} description="请上传文件开始" />}
          </div>
          {/* <FloatButton
            icon={<ArrowDownOutlined />}
            onClick={() => {
              scrollToBottom(leftColumnRef.current as HTMLDivElement);
            }}
          /> */}
          <div style={fixedFooterStyle}>
            <Divider style={{ marginTop: 0 }} />
            <input
              key={fileDomKey}
              type="file"
              ref={fileRef}
              accept=".gz,.zip,.tar"
              hidden
              onChange={async (e: any) => {
                const fileName = e.target?.files?.[0]?.name || "代码文件";
                console.log("restart:", fileName);
                try {
                  // exists.current = false; // 不存在了
                  setMessageList([]);
                  setDetail([]);
                  setFileDomKey((prev) => prev + 1);
                  // await apiUpload(e.target?.files?.[0]);
                  // await apiStart();
                  await player(e.target?.files?.[0]);
                } catch (error) {
                  // message.error("上传失败");
                  console.log(error);
                } finally {
                  setLoading(false);
                }
              }}
            />
            <Flex>
              <Button
                block
                type="primary"
                disabled={loading}
                icon={<UploadOutlined />}
                onClick={() => {
                  fileRef.current?.click();
                }}
              >
                选择文件
              </Button>
              {/* <Button style={{ marginLeft: "10px" }}>停止</Button> */}
            </Flex>
          </div>
        </div>
        <Divider type="vertical" style={{ height: "100%", margin: 0 }} />
        {/* 右列（纯滚动） */}
        <div style={rightColumnStyle} ref={rightColumnRef} className="custom-scrollbar">
          <div
            style={{
              padding: "20px",
              whiteSpace: "pre-wrap",
              wordBreak: "break-all",
            }}
          >
            {detail?.length > 0 && (
              <Collapse
                activeKey={activeDetailKey}
                onChange={(key: string | string[]) => {
                  // console.log(key);
                  setActiveDetailKey(key);
                }}
                items={detail.map((item) => ({
                  key: item.id,
                  label: <div id={item.id}>{item.title}</div>,
                  forceRender: true,
                  children: item.body,
                }))}
              />
            )}
            {detail?.length === 0 && <Empty style={{ marginTop: "150px" }} description="暂无数据" />}
            {/* {detail.map((item) => (
              <div key={item.id} id={item.id}>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Space>
                    <Text>{item.title}</Text>
                  </Space>
                  {item.body}
                </Space>

                <Divider />
              </div>
            ))} */}
          </div>
        </div>
      </Content>
    </div>
  );
};

export default Demo;
