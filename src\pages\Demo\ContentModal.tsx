import { Drawer, Modal, Space, Typography } from "antd";
import { useState, type ReactNode } from "react";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/cjs/styles/prism";
import MdBlock from "./MdBlock";
import CodeBlock from "./CodeBlock";

export default function ContentModal({
  title,
  content,
  children,
  type = "markdown",
}: {
  title: ReactNode;
  content: ReactNode;
  children?: ReactNode;
  type?: "markdown" | "bash";
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div onClick={() => setIsOpen(true)}>{children}</div>
      <Drawer width="80%" title={title} footer={null} open={isOpen} onClose={() => setIsOpen(false)}>
        {type === "markdown" && <CodeBlock text={String(content)} language="md" />}
        {type === "bash" && <CodeBlock text={String(content)} language="bash" />}
      </Drawer>
    </>
  );
}
