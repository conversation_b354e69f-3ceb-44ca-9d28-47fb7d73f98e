"污染源（request : HttpServletRequest）：
sdf

```
@Controller
@RequestMapping("/sql")
public class SQLController {
  @RequestMapping("/hql/case1")
@ResponseBody
public Object HQLCase1(HttpServletRequest request, HttpServletResponse response) throws Exception {
            String id  = request.getParameter("id");
        System.out.println(id);
        String username = request.getParameter("username");
        System.out.println(username);
        String password = request.getParameter("password");
        System.out.println(password);
        List<Object> results = HQLInject.select(Integer.valueOf(id), username, password);
        return results;
    }

  }
}
```

中间步骤：

- Step 1 [SQLController.java:HQLCase1]: String id = request.getParameter("id");
- Step 2 [SQLController.java:HQLCase1]: String username = request.getParameter("username");
- Step 3 [SQLController.java:HQLCase1]: String password = request.getParameter("password");
- Step 4 [SQLController.java:HQLCase1]: String password = request.getParameter("password");
- Step 5 [SQLController.java:HQLCase1]: List<Object> results = HQLInject.select(Integer.valueOf(id), username, password);
- Step 6 [HQLInject.java:select]: public static List<Object> select(Integer id, String username, String password) throws Exception {
- Step 7 [HQLInject.java:select]: sql = String.format("select \* from users where `username`='%s' and `password`='%s';", username, password);
- Step 8 [HQLInject.java:select]: sql = String.format("select \* from users where `username`='%s' and `password`='%s';", username, password); s

污染汇（sql）：

```
public class HQLInject {
  public static List<Object> select(Integer id, String username, String password) throws Exception {
            //Hibernate 加载核心配置文件（有数据库连接信息）
        Configuration configuration = new Configuration().configure();
        //创建一个 SessionFactory 用来获取 Session 连接对象
        SessionFactory sessionFactory = configuration.buildSessionFactory();
        //获取session 连接对象
        Session session = sessionFactory.openSession();
        //开始事务
        Transaction transaction = session.beginTransaction();
        String sql = null;
        if (username != null && password != null) {
            sql = String.format("select * from users where `username`='%s' and `password`='%s';", username, password);
        } else {
            return null;
        }
        NativeQuery sqlQuery = session.createSQLQuery(sql); // <---- THIS IS THE SINK
        sqlQuery.addEntity(Users.class);
        List<Object> users = new ArrayList();
        List<Users> rows = sqlQuery.list();
        if (rows.size() > 0) {
            for (Users o : rows) {
                users.add(new Users(o.getId(), o.getUsername(), o.getPassword()));
            }
            //提交事务
            transaction.commit();
            //释放资源
            session.close();
            sessionFactory.close();
        }
        return users;
    }

  }
}
```
