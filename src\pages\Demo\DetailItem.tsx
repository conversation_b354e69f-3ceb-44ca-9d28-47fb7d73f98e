import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  FileOutlined,
  FileSearchOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { Alert, Button, Card, Collapse, Space, Steps, Tag, Typography } from "antd";
import type { StepItem } from "./types";

const { Step } = Steps;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

// 步骤数据结构
const steps = [
  {
    title: "API接口梳理",
    time: "用时1分20秒",
    tasks: [
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "梳理函数调用接口",
        content: (
          <Paragraph style={{ margin: 0 }}>
            共计找到接口 <Tag color="blue">63个</Tag>
          </Paragraph>
        ),
      },
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "接口分类",
        token: 8,
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  请将查询到的接口按污点源（Source）、传播点（Trans）、污点汇聚点（Sink）类型进行分类
                </Text>
              }
              key="1"
              style={{ backgroundColor: "transparent" }}
            >
              <div style={{ marginLeft: 24 }}>
                <Paragraph style={{ margin: 0 }}>
                  <Text strong>分类完成。</Text>
                </Paragraph>
                <Space size="middle">
                  <Tag color="red">污点源（Source）5个</Tag>
                  <Tag color="orange">传播点（Trans）12个</Tag>
                  <Tag color="green">污点汇聚点（Sink）6个</Tag>
                </Space>
              </div>
            </Panel>
          </Collapse>
        ),
      },
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "分析Source-Trans-Sink调用过程",
        content: (
          <Paragraph style={{ margin: 0 }}>
            查找完成。找到调用成功的调用链 <Tag color="blue">12个</Tag>
          </Paragraph>
        ),
      },
    ],
  },
  {
    title: "漏洞检测",
    time: "用时1分20秒",
    token: 16,
    tasks: [
      {
        icon: <RobotOutlined style={{ color: "#faad14" }} />,
        title: "漏洞检测",
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  你是一名安全专家。分析这个 Java
                  项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。请注意可疑的错误消息有时可能...
                </Text>
              }
              key="2"
              style={{ backgroundColor: "transparent" }}
            >
              <Alert
                type="error"
                showIcon
                icon={<ExclamationCircleOutlined />}
                message={
                  <span>
                    共计发现{" "}
                    <Text strong type="danger">
                      2
                    </Text>{" "}
                    个漏洞！
                  </span>
                }
                style={{ marginLeft: 24 }}
              />
            </Panel>
          </Collapse>
        ),
      },
    ],
  },
  {
    title: "漏洞研判",
    time: "用时20秒",
    tasks: [
      {
        icon: <RobotOutlined style={{ color: "#faad14" }} />,
        title: "自动生成POC",
        token: 10,
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  你是一个专门研究RCE漏洞的验证和利用的研究员，你精通Nuclei漏洞验证框架。你要根据下面的漏洞描述，生成nuclei可用的poc...
                </Text>
              }
              key="3"
              style={{ backgroundColor: "transparent" }}
            >
              <Paragraph style={{ marginLeft: 24, marginBottom: 0 }}>
                执行完成。一共生成 <Tag color="blue">2个POC</Tag>
              </Paragraph>
            </Panel>
          </Collapse>
        ),
      },
      {
        icon: <CheckCircleOutlined style={{ color: "#52c41a" }} />,
        title: "POC验证",
        content: (
          <Paragraph style={{ margin: 0 }}>
            验证完成。成功 <Tag color="green">2个</Tag>，失败 <Tag color="red">0个</Tag>
          </Paragraph>
        ),
      },
    ],
  },
];

const stepNameMap = {
  1: "第一步：导入源代码文件",
  2: "第二步：代码预处理",
  3: "第三步：API接口梳理",
  4: "第四步：漏洞检测",
  5: "第五步：漏洞研判",
  6: "第六步：修复建议",
};

type Props = StepItem & {
  onDetailClick: (detail: string, title: string) => void;
};

function DetailItem({ step, stepSpendToken, stepSpendTime, taskList, onDetailClick }: Props) {
  if ([1, 2, 3, 4, 5].includes(step)) {
    return (
      <Card
        // style={{ boxShadow: "0 2px 12px #f0f1f2" }}
        title={stepNameMap[step]}
      >
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <Space direction="vertical" style={{ width: "100%" }} size="middle">
            <Space>
              <ClockCircleOutlined style={{ color: "#1890ff" }} />
              {step == 1 && <Text strong>代码文件：source.jar</Text>}
              {step == 2 && <Text strong>对源码进行静态分析，生成抽象语法树</Text>}
              {step == 3 && <Text strong>梳理函数调用接口</Text>}
              {step == 4 && <Text strong>漏洞检测</Text>}
              {step == 5 && <Text strong>漏洞研判</Text>}
              <Space>
                {!!stepSpendTime && (
                  <>
                    <ClockCircleOutlined style={{ color: "#1890ff" }} />
                    <Text type="secondary">用时{stepSpendTime}</Text>
                  </>
                )}
                {!!stepSpendToken && <Tag color="purple">消耗Token{stepSpendToken}个</Tag>}
              </Space>
            </Space>
            {taskList.map((task, tIdx) => (
              <Card key={task.key} type="inner" style={{ background: "#f6f8fa", borderRadius: 12 }}>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Space align="center" style={{ justifyContent: "space-between", width: "100%" }}>
                    <Space>
                      <FileOutlined style={{ color: "#faad14" }} />
                      <Text strong>{`任务${tIdx + 1}：${task.name}`}</Text>
                    </Space>
                  </Space>

                  <Paragraph style={{ marginTop: 8 }} ellipsis>
                    {task.description}
                  </Paragraph>
                  <Text
                    className="show-detail"
                    type="secondary"
                    onClick={() => onDetailClick(task.description as string, task.name)}
                  >
                    查看详情
                  </Text>

                  {step == 4 && (
                    <Alert
                      type="error"
                      message="共计发现2个漏洞！"
                      showIcon
                      onClick={() => onDetailClick(task.description as string, task.name)}
                    />
                  )}
                </Space>
              </Card>
            ))}
          </Space>
        </Space>
      </Card>
    );
  }
  if (step == 6) {
    return (
      <Card style={{ boxShadow: "0 2px 12px #f0f1f2" }} title={stepNameMap[step]}>
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <Space direction="vertical" style={{ width: "100%" }} size="middle">
            <Space>
              <Text strong>漏洞统计和建议</Text>
              <Space>
                {!!stepSpendTime && (
                  <>
                    <ClockCircleOutlined style={{ color: "#1890ff" }} />
                    <Text type="secondary">用时{stepSpendTime}</Text>
                  </>
                )}
                {!!stepSpendToken && <Tag color="purple">消耗Token{stepSpendToken}个</Tag>}
              </Space>
            </Space>
            <Space>
              <Space>
                <Text>高危漏洞</Text> <Tag color="purple">9</Tag>
              </Space>
              <Space>
                <Text>中危漏洞</Text> <Tag color="purple">32</Tag>
              </Space>
              <Space>
                <Text>低危漏洞</Text> <Tag color="purple">44</Tag>
              </Space>
            </Space>

            {/* 修复建议 */}
          </Space>
        </Space>
      </Card>
    );
  }

  return step;
}
export default DetailItem;
