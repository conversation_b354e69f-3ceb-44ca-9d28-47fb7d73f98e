import generateFile from './generate-api.js';
// node ./generate-api/daily.js

generateFile({
  filename: 'analysis-judge', // 文件名
  src: 'http://192.168.0.250:21001/v3/api-docs/ngsoc-assets', // swagger 文档地址
  outputDir: 'src/api/generated', // 输出目录
  // 自定义文件头
  fileHead: `/* 威胁管理系统-分析中心-研判分析 */\n`,
  include: [
    '/threat/analysisCenter/page',
    '/threat/analysisCenter/deleteFile',
    '/threat/analysisCenter/company/update',
    '/threat/analysisCenter/titleCount',
    '/threat/analysisCenter/types',
    '/threat/analysisCenter/company/detail/{id}',
    '/threat/analysisCenter/titleCount',
    '/threat/analysisCenter/log/detail/{id}',
    '/threat/analysisCenter/company/infoBack/{id}',
    '/threat/analysisCenter/log/update'
  ]
});
