import { createRoot } from "react-dom/client";
import { PageRouter } from "./router/index.tsx";
import { BrowserRouter } from "react-router";
import { ConfigProvider } from "antd";
import { Provider } from "react-redux";
import { store } from "./store";
import { setDefaultTheme } from "amis";
import "./style.less";
import "amis/lib/themes/antd.css";

console.log("base_url:", import.meta.env.VITE_API_BASE_URL);

setDefaultTheme("antd");

createRoot(document.getElementById("root")!).render(
  <Provider store={store}>
    <ConfigProvider>
      <BrowserRouter>
        <PageRouter />
      </BrowserRouter>
    </ConfigProvider>
  </Provider>,
);
