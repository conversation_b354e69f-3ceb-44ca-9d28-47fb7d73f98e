import { AlertOutlined, <PERSON><PERSON><PERSON>cleOutlined, <PERSON>Outlined, RobotFilled, UserOutlined } from "@ant-design/icons";
import {
  <PERSON>ert,
  Avatar,
  Button,
  Card,
  Col,
  Collapse,
  Divider,
  List,
  Row,
  Space,
  Spin,
  Table,
  Tag,
  Typography,
} from "antd";
import { sleep } from "../../utils/sleep";
import { type TimeLineItem } from "./types";
// import { Prompts } from "@ant-design/x";
import { apiLoop } from "../../apis/file";
import ContentModal from "./ContentModal";
import MyBubble from "./MyBubble";
import { type DetailItemType } from "./types";
import CodeBlock from "./CodeBlock";
import MdBlock from "./MdBlock";

const headPadding = "10px 0 15px 0";

export default function DataContent({ data, maxStep }: { data: Record<string, TimeLineItem>; maxStep: number }) {
  return (
    <>
      {/* 1 */}
      {data["1"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>导入源代码文件</MyBubble>
        </Space>
      )}
      {data["1-0"] && (
        <Card
          title={
            <Space>
              {/* <CoffeeOutlined style={{ color: "blue" }} /> */}
              <MyBubble>上传文件</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical">
            <Space>
              <Spin size="small" />
              <MyBubble>文件上传中</MyBubble>
            </Space>
            <Space>
              <CheckCircleOutlined style={{ color: "green" }} />
              <a href="#1">上传成功</a>
            </Space>
          </Space>
        </Card>
      )}

      {/* 2 */}
      {data["2"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>代码预处理</MyBubble>
        </Space>
      )}

      {data["2-1"] && (
        <Card
          title={
            <Space>
              {/* <CoffeeOutlined style={{ color: "blue" }} /> */}
              <MyBubble>对源码进行静态分析，生成抽象语法树</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical">
            <Space>
              <Spin size="small" />
              处理中
            </Space>
            <Space>
              <CheckCircleOutlined style={{ color: "green" }} />
              处理完成
            </Space>
          </Space>
        </Card>
      )}
      {/* 3 */}
      {data["3"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>API接口梳理</MyBubble>
        </Space>
      )}
      {data["3-1"] && (
        <Card
          title={
            <Space>
              <MyBubble>梳理函数调用接口</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务一：梳理函数调用接口</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">{title}</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务二：接口分类</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space align="start">
                  <AlertOutlined style={{ color: "blue" }} />
                  请将查询查询到的接口按污点源（Source）、传播点（Trans）、污点汇聚点（Sink）类型进行分类，并按照该数据格式进行返回
                </Space>
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">{title}</a> */}
                  分类完成。污点源（Source）5个，传播点（Trans）12个，污点汇聚点（Sink）6个
                </Space>
              </Space>
            </Card>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  任务三：分析污点源（Source）、传播点（Trans）、污点汇聚点（Sink）调用过程，查找能成功调用的调用链
                </Space>
              }
            >
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space direction="vertical">
                  <Space>
                    <Spin size="small" />
                    处理中
                  </Space>
                  <Space>
                    <CheckCircleOutlined style={{ color: "green" }} />
                    {/* <a href="#3-3"></a> */}
                    查找完成。找到调用成功的调用链12个
                  </Space>
                </Space>
              </Space>
            </Card>
          </Space>
        </Card>
      )}
      {/* 4 */}
      {data["4"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>漏洞检测</MyBubble>
        </Space>
      )}
      {data["4-1"] && (
        <Card
          title={
            <Space>
              <MyBubble>漏洞检测</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务一：RCE命令注入漏洞检测</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space align="start">
                  <AlertOutlined style={{ color: "blue" }} />
                  你是一名安全专家。分析这个 Java
                  项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。
                </Space>
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">共计发现2个漏洞！</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
          </Space>
        </Card>
      )}
      {/* 5 */}
      {data["5"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>漏洞研判</MyBubble>
        </Space>
      )}
      {data["5-1"] && (
        <Card
          title={
            <Space>
              <MyBubble>漏洞研判</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务一：生成RCE命令注入POC脚本</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space align="start">
                  <AlertOutlined style={{ color: "blue" }} />
                  你是一个专门研究RCE漏洞的验证和利用的研究员，你精通 Nuclei 漏洞验证框架。你要根据下面的漏洞描述，
                  生成nuclei可用的poc，需要严格符合nuclei的模板规范，符合 Nuclei v3 语法规范，通过 nuclei -validate
                  语法验证， 我将基于此模板进行漏洞验证。 Java
                  项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。
                </Space>
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">执行完成。共计生成5个POC</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务二：生成SQL注入POC脚本</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space align="start">
                  <AlertOutlined style={{ color: "blue" }} />
                  你是一个专门研究SQL注入漏洞的验证和利用的研究员，你精通 Nuclei
                  漏洞验证框架。你要根据下面的漏洞描述，生成nuclei可用的poc。
                </Space>
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">执行完成。共计生成2个POC</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务三：POC验证</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">验证完成，成功 2 个，失败 3 个</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
          </Space>
        </Card>
      )}
      {/* 6 */}
      {data["6"] && (
        <Space style={{ padding: headPadding }}>
          <Avatar icon={<RobotFilled />} />
          <MyBubble style={{ fontWeight: "bold", fontSize: "16px" }}>修复建议</MyBubble>
        </Space>
      )}
      {data["6-1"] && (
        <Card
          title={
            <Space>
              <MyBubble>修复建议</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Card
              type="inner"
              title={
                <Space>
                  <CoffeeOutlined style={{ color: "blue" }} />
                  <MyBubble>任务一：生成修复建议</MyBubble>
                </Space>
              }
            >
              <Space direction="vertical">
                <Space align="start">
                  <AlertOutlined style={{ color: "blue" }} />
                  角色： 代码审计专家，擅长发现并修复代码中的安全漏洞。 背景信息：
                  接收到一段存在安全漏洞的代码，需要对其进行详细分析并提出修复建议。 工作流程/工作任务：
                  1.仔细阅读并分析提供的代码，识别出其中的安全漏洞。2. 针对每个识别出的漏洞，提出具体的修复建议。3.
                  编写一份详细的审计报告，包括漏洞描述、修复建议和预防措施。
                </Space>
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-1">修复建议已生成</a> */}
                  处理完成
                </Space>
              </Space>
            </Card>
          </Space>
        </Card>
      )}
    </>
  );
}

export function Step3({ id, step, description, state }: TimeLineItem) {
  return (
    <Card
      title={
        <Space>
          <MyBubble>梳理函数调用接口</MyBubble>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Card
          type="inner"
          title={
            <Space>
              <CoffeeOutlined style={{ color: "blue" }} />
              <MyBubble>任务一：梳理函数调用接口</MyBubble>
            </Space>
          }
        >
          <Space direction="vertical">
            <Space>
              <Spin size="small" />
              处理中
            </Space>
            <Space>
              <CheckCircleOutlined style={{ color: "green" }} />
              {/* <a href="#3-1">{title}</a> */}
              处理完成
            </Space>
          </Space>
        </Card>

        {id === "3-1" && (
          <Card
            type="inner"
            title={
              <Space>
                <CoffeeOutlined style={{ color: "blue" }} />
                <MyBubble>任务二：接口分类</MyBubble>
              </Space>
            }
          >
            <Space direction="vertical">
              <Space align="start">
                <AlertOutlined style={{ color: "blue" }} />
                请将查询查询到的接口按污点源（Source）、传播点（Trans）、污点汇聚点（Sink）类型进行分类，并按照该数据格式进行返回
              </Space>
              <Space>
                <Spin size="small" />
                处理中
              </Space>
              <Space>
                <CheckCircleOutlined style={{ color: "green" }} />
                {/* <a href="#3-1">{title}</a> */}
                分类完成。污点源（Source）5个，传播点（Trans）12个，污点汇聚点（Sink）6个
              </Space>
            </Space>
          </Card>
        )}
        {id === "3-2" && (
          <Card
            type="inner"
            title={
              <Space>
                <CoffeeOutlined style={{ color: "blue" }} />
                任务三：分析污点源（Source）、传播点（Trans）、污点汇聚点（Sink）调用过程，查找能成功调用的调用链
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: "100%" }}>
              <Space direction="vertical">
                <Space>
                  <Spin size="small" />
                  处理中
                </Space>
                <Space>
                  <CheckCircleOutlined style={{ color: "green" }} />
                  {/* <a href="#3-3"></a> */}
                  查找完成。找到调用成功的调用链12个
                </Space>
              </Space>
            </Space>
          </Card>
        )}
      </Space>
    </Card>
  );
}
