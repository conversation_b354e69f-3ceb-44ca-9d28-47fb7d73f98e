import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON>,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tag,
  Typography,
} from "antd";
import { sleep } from "../../utils/sleep";
import { type TimeLineItem } from "./types";
// import { Prompts } from "@ant-design/x";
import { apiLoop, apiStart, apiUpload } from "../../apis/file";
import CodeBlock from "./CodeBlock";
import ContentModal from "./ContentModal";
import MyBubble from "./MyBubble";
import { type DetailItemType } from "./types";
const { Title, Text, Paragraph } = Typography;
const marginLeft = "30px";

const { Panel } = Collapse;
export function getTimeLine({
  file,
  setDetail,
  setActiveDetailKey,
  exists,
}: {
  file?: File;
  setDetail: (fn: (old: DetailItemType[]) => DetailItemType[]) => void;
  setActiveDetailKey: (key: string) => void;
  exists: boolean;
}) {
  console.log("getTimeLine start:", file?.name, exists);
  const fileSpend = Date.now();

  function updateDetailList(data: DetailItemType) {
    setDetail((prev) => {
      if (!prev.find((el) => el.id === data.id)) {
        return [...prev, data];
      }
      return prev.map((item) => (item.id === data.id ? data : item));
    });
  }
  return [
    /*{
      request: async (): Promise<TimeLineItem> => {
        return {
          id: "1",
          step: 1,
          description: {},
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        return {
          id: "1-0",
          step: 1,
          description: {},
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (file) {
          const uploadRes = await apiUpload(file, {
            onProgress(percent) {
              console.log("file %", percent);
            },
          });
          if (uploadRes != "1") {
            Modal.warning({
              title: "提示",
              content: uploadRes,
              onOk: () => {
                window.location.reload();
              },
            });
            await sleep(6000 * 1000); // 相当于卡在这里
          }
        }

        return {
          id: "1-0",
          step: 1,
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        const res = await apiLoop({ id: "1-1", step: 1 });
        console.log("1-1:", res);
        updateDetailList({ id: "1", title: "文件上传", body: file?.name || "文件上传成功" });
        // setActiveDetailKey("1");
        const spendTime = exists ? "" : (Date.now() - fileSpend) / 1000;
        return {
          spendTime,
          ...res,
          id: "1-1",
          step: 1,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "2",
          step: 2,
          description: {},
          state: 1,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "2-0",
          step: 2,
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        //

        const res = await apiLoop({ id: "2-1", step: 2 });
        console.log("2-1:", res);

        return {
          ...res,
          id: "2-1",
          step: 2,
        };
      },
    },
    // ------------------- 3 -------------------
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }

        return {
          id: "3",
          step: 3,
          description: {},
          state: 1,
        };
      },
    },
    */
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        if (file) {
          // 文件上传成功后，开始执行
          apiStart();
        }
        return {
          id: "3-0",
          step: 3,
          description: {},
          state: 1,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        const res = await apiLoop({ id: "3-1", step: 3 });
        console.log("3-1:", res);
        const { description } = res;

        if (description) {
          updateDetailList({
            id: "3-1",
            title: (
              <>
                梳理函数调用接口：找到接口 <Text strong>{description?.length}</Text> 个
              </>
            ),
            body: (
              <div style={{ overflow: "auto" }}>
                <Table
                  rowKey="index"
                  pagination={false}
                  columns={[
                    { title: "序号", dataIndex: "index", minWidth: 65 },
                    { title: "包名", dataIndex: "pkg", minWidth: 120 },
                    { title: "类", dataIndex: "clazz", minWidth: 120 },
                    { title: "方法", dataIndex: "func", minWidth: 120 },
                    { title: "方法签名", dataIndex: "fullSignature", minWidth: 120 },
                  ]}
                  dataSource={description?.map?.((el, index) => ({ ...el, index: index + 1 }))}
                />
              </div>
            ),
          });
          // setActiveDetailKey("3-1");
        }
        return {
          id: "3-1",
          step: 3,
          description,
          state: res.state,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(1000);
        }
        const res = await apiLoop({ id: "3-2", step: 3 });
        console.log("3-2:", res);
        const { description } = res;
        const { sourceCount, transCount, sinkCount, sinkApis, sourceApis, transApis } = description || {};
        const title = (
          <>
            接口分类： 污点源（Source）<Text strong>{sourceCount}</Text>
            个，传播点（Trans）<Text strong>{transCount}</Text>
            个，污点汇聚点（Sink）<Text strong>{sinkCount}</Text>个。
          </>
        );
        if (res.description) {
          updateDetailList({
            id: "3-2",
            title: title,
            body: (
              <Collapse
                items={[
                  {
                    key: "污点源（Source）",
                    label: "污点源（Source）",
                    children: (
                      <Table
                        rowKey="index"
                        pagination={false}
                        columns={[
                          { title: "序号", dataIndex: "index", width: 65 },
                          { title: "包名", dataIndex: "package", minWidth: 120 },
                          { title: "类", dataIndex: "class", minWidth: 80 },
                          { title: "方法", dataIndex: "method", minWidth: 80 },
                          { title: "方法签名", dataIndex: "signature", minWidth: 100 },
                        ]}
                        dataSource={sourceApis?.map?.((el, index) => ({ ...el, index: index + 1 }))}
                      />
                    ),
                  },
                  {
                    key: "传播点（Trans）",
                    label: "传播点（Trans）",
                    children: (
                      <Card size="small" type="inner" title="">
                        <Table
                          rowKey="index"
                          pagination={false}
                          columns={[
                            { title: "序号", dataIndex: "index", width: 65 },
                            { title: "包名", dataIndex: "package", minWidth: 120 },
                            { title: "类", dataIndex: "class", minWidth: 80 },
                            { title: "方法", dataIndex: "method", minWidth: 80 },
                            { title: "方法签名", dataIndex: "signature", minWidth: 100 },
                          ]}
                          dataSource={transApis?.map?.((el, index) => ({ ...el, index: index + 1 }))}
                        />
                      </Card>
                    ),
                  },
                  {
                    key: "污点汇聚点（Sink）",
                    label: "污点汇聚点（Sink）",
                    children: (
                      <Table
                        rowKey="index"
                        pagination={false}
                        columns={[
                          { title: "序号", dataIndex: "index", width: 65 },
                          { title: "包名", dataIndex: "package", minWidth: 120 },
                          { title: "类", dataIndex: "class", minWidth: 80 },
                          { title: "方法", dataIndex: "method", minWidth: 80 },
                          { title: "方法签名", dataIndex: "signature", minWidth: 100 },
                        ]}
                        dataSource={sinkApis?.map?.((el, index) => ({ ...el, index: index + 1 }))}
                      />
                    ),
                  },
                ]}
              />
            ),
          });
          // setActiveDetailKey("3-2");
        }
        return {
          ...res,
          id: "3-2",
          sourceCount,
          transCount,
          sinkCount,
          step: 3,
          description: { ...description },
          state: res.state,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(2000);
        }
        const res = await apiLoop({ id: "3-3", step: 3 });
        console.log("3-3:", res);

        const { description = {} } = res;

        updateDetailList({
          id: "3-3",
          title: (
            <>
              找到调用成功的调用链
              <Text strong>
                {Object.entries(description).reduce((pre, cur: any) => pre + (cur?.[1]?.length ?? 0), 0)}
              </Text>
              个
            </>
          ),
          body: (
            <Collapse
              items={Object.entries(description).map(([key, value]: any) => ({
                key,
                label: key,
                children: (
                  <Space wrap style={{ width: "100%" }}>
                    {value.map((item: any, i: number) => (
                      <Button key={i}>{item}</Button>
                    ))}
                  </Space>
                ),
              }))}
            />
          ),
        });
        // setActiveDetailKey("3-3");

        return {
          ...res,
          description,
          id: "3-3",
          step: 3,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "4",
          step: 4,
          // content: <Typography.Title level={4}>第四步：漏洞检测</Typography.Title>,
          description: {},
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "4-0",
          step: 4,
          description: {},
          state: 1,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(1000);
        }
        const res = await apiLoop({ id: "4-1", step: 4 });
        console.log("4-1:", res);
        const { description, spendTime, token } = res;

        if (description) {
          const total = Object.keys(description).reduce((pre, cur) => pre + description[cur].length, 0);
          updateDetailList({
            id: "4-1",
            title: (
              <>
                RCE命令注入漏洞检测：发现 <Text strong>{total}</Text> 个漏洞
              </>
            ),
            body: (
              <Collapse
                items={Object.entries(description).map(([key, value]: any) => ({
                  key,
                  label: key,
                  children: (
                    <div style={{ overflow: "auto" }}>
                      <Table
                        size="small"
                        rowKey="id"
                        pagination={false}
                        columns={[
                          { title: "序号", dataIndex: "id", minWidth: 65 },
                          { title: "漏洞类型", dataIndex: "vulnerable_type", minWidth: 100 },
                          { title: "缺陷名称", dataIndex: "sink_uri", minWidth: 250 },
                          { title: "缺陷行号", dataIndex: "sink_line", minWidth: 100 },
                          { title: "检出原因", dataIndex: "explanation", minWidth: 160 },
                          { title: "可信度", dataIndex: "confidence_desc", minWidth: 80 },
                          { title: "检出时间", dataIndex: "create_time", minWidth: 120 },
                          {
                            title: "详情",
                            dataIndex: "callChain",
                            fixed: "right",
                            minWidth: 100,
                            render(text) {
                              return (
                                <ContentModal type="markdown" title="详情" content={text}>
                                  <Button type="link" size="small">
                                    详情
                                  </Button>
                                </ContentModal>
                              );
                            },
                          },
                        ]}
                        dataSource={value}
                      />
                    </div>
                  ),
                }))}
              />
            ),
          });
          // setActiveDetailKey("4-1");
        }
        return {
          ...res,
          id: "4-1",
          step: 4,
          spendTime,
          token,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "5",
          step: 5,
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "5-0",
          step: 5,
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        const res = await apiLoop({ id: "5-1", step: 5 });
        console.log("5-1:", res);
        const { description, spendTime, token } = res || {};
        const { pocInfo, resulInfo } = description || {};
        const { statistics, pocs } = pocInfo || {};
        const RCEStatistics = statistics?.find((item) => item.vulType === "RCE") || {};
        const SQLStatistics = statistics?.find((item) => item.vulType === "SQL注入") || {};

        updateDetailList({
          id: "5-1",
          title: (
            <>
              生成RCE命令注入POC脚本：生成 <Text strong>{RCEStatistics?.generatedCount}</Text> 个POC
            </>
          ),
          body: (
            <List
              size="small"
              bordered
              header={"RCE"}
              dataSource={pocs?.["RCE"] || []}
              renderItem={(item) => (
                <List.Item>
                  <div style={{ overflow: "auto" }}>
                    <CodeBlock text={String(item)} language="yaml" />
                  </div>
                </List.Item>
              )}
            />
          ),
        });
        // setActiveDetailKey("5-1");

        updateDetailList({
          id: "5-2",
          title: (
            <>
              生成SQL注入POC脚本：生成 <Text strong>{SQLStatistics?.generatedCount}</Text> 个POC
            </>
          ),
          body: (
            <List
              size="small"
              bordered
              header={"SQL注入"}
              dataSource={pocs?.["SQL注入"] || []}
              renderItem={(item) => (
                <List.Item>
                  <div style={{ overflow: "auto" }}>
                    <CodeBlock text={String(item)} language="yaml" />
                  </div>
                </List.Item>
              )}
            />
          ),
        });
        // setActiveDetailKey("5-2");

        updateDetailList({
          id: "5-3",
          title: (
            <>
              根据靶机利用POC动态验证漏洞：成功 <Text strong>{resulInfo?.success}</Text>个，失败{" "}
              <Text strong>{resulInfo?.failed}</Text>个
            </>
          ),
          body: (
            <div style={{ overflow: "auto" }}>
              <Table
                pagination={false}
                rowKey="id"
                columns={[
                  { title: "序号", dataIndex: "id", minWidth: 60 },
                  { title: "类型", dataIndex: "vulType", minWidth: 60 },
                  { title: "名称", dataIndex: "sinkUri", minWidth: 160 },
                  { title: "行号", dataIndex: "sinkLine", minWidth: 60 },
                  { title: "靶机路径", dataIndex: "url", minWidth: 200 },
                  {
                    title: "验证结果",
                    minWidth: 180,
                    dataIndex: "flag",
                    render(text) {
                      if (text?.match("成功")) {
                        return <Tag color="success">验证成功</Tag>;
                      }
                      if (text?.match("失败")) {
                        return <Tag color="error">验证失败</Tag>;
                      }
                      return <Spin size="small" />;
                    },
                  },
                  { title: "验证时间", dataIndex: "verifyTime", minWidth: 120 },
                  {
                    title: "详情",
                    fixed: "right",
                    dataIndex: "nucleiProcess",
                    minWidth: 80,
                    render(text) {
                      return (
                        <ContentModal type="bash" title="详情" content={text}>
                          <Button type="link" size="small">
                            查看
                          </Button>
                        </ContentModal>
                      );
                    },
                  },
                ]}
                dataSource={resulInfo?.resultList || []}
              />
            </div>
          ),
        });
        // setActiveDetailKey("5-3");

        return {
          ...res,
          id: "5-1",
          step: 5,
          description: {},
          state: res.state,

          task1has: RCEStatistics?.generatedCount,
          task1total: RCEStatistics?.totalCount,
          task2has: SQLStatistics?.generatedCount,
          task2total: SQLStatistics?.totalCount,
          task3has: resulInfo?.generatedNum,
          task3total: resulInfo?.total,
          task3success: resulInfo?.success,
          task3fail: resulInfo?.failed,
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "6",
          step: 6,
          description: {},
          state: 1,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "6-0",
          step: 6,
          state: 1,
          description: {},
        };
      },
    },

    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        const res = await apiLoop({ id: "6-1", step: 6 });
        console.log("6-1:", res);

        const { description, spendTime, token } = res;
        const { total, generateNum, highVulNum, mediumVulNum, lowVulNum, sugestionList = [] } = description;

        if (description) {
          updateDetailList({
            id: "6-1",
            title: "修复建议",
            body: (
              <div style={{ overflow: "auto" }}>
                <Space direction="vertical">
                  <Row>
                    <Col span={4}>
                      <Space>
                        高危漏洞 <Tag color="error">{highVulNum}</Tag>
                      </Space>
                    </Col>
                    <Col span={4}>
                      <Space>
                        中危漏洞 <Tag color="warning">{mediumVulNum}</Tag>
                      </Space>
                    </Col>
                    <Col span={4}>
                      <Space>
                        低危漏洞 <Tag>{lowVulNum}</Tag>
                      </Space>
                    </Col>
                  </Row>
                  <Divider style={{ margin: "10px 0" }} />
                  <Table
                    pagination={false}
                    rowKey="index"
                    columns={[
                      { title: "序号", dataIndex: "index", minWidth: 60 },
                      { title: "漏洞类型", dataIndex: "vulType", minWidth: 100 },
                      { title: "缺陷名称", dataIndex: "sinkUri", minWidth: 160 },
                      { title: "缺陷行号", dataIndex: "sinkLine", minWidth: 100 },
                      { title: "可信度", dataIndex: "zhixindu", minWidth: 80 },
                      {
                        title: "危险程度",
                        dataIndex: "level",
                        minWidth: 100,
                        render(text) {
                          return <Text type={{ 高危: "danger", 中危: "warning" }[text]}>{text}</Text>;
                        },
                      },
                      {
                        title: "验证结果",
                        dataIndex: "flag",
                        minWidth: 100,
                        render(text) {
                          if (text?.match("成功")) {
                            return <Tag color="success">验证成功</Tag>;
                          }
                          if (text?.match("失败")) {
                            return <Tag color="error">验证失败</Tag>;
                          }
                          return <Spin size="small" />;
                        },
                      },
                      { title: "更新时间", dataIndex: "finishTime", minWidth: 120 },
                      {
                        title: "修复建议",
                        dataIndex: "advice",
                        fixed: "right",
                        minWidth: 100,
                        render(text: string, record: any) {
                          return (
                            <ContentModal type="markdown" title="修复建议" content={text}>
                              <Button type="link" size="small">
                                查看
                              </Button>
                            </ContentModal>
                          );
                        },
                      },
                    ]}
                    dataSource={sugestionList.map((el, index) => ({ ...el, index: index + 1 }))}
                  />
                </Space>
              </div>
            ),
          });
          // setActiveDetailKey("6-1");
        }
        return {
          ...res,
          id: "6-1",
          step: 6,
          description,
          generateNum,
          total,
          state: res.state,
        };
      },
    },
    {
      request: async (): Promise<TimeLineItem> => {
        if (!exists) {
          await sleep(500);
        }
        return {
          id: "7",
          step: 7,
          description: {},
          state: 1,
        };
      },
    },
  ];
}
