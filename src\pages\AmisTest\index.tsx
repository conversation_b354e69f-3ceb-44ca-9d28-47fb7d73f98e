import { render as renderAmis } from "amis";

export default function AmisTest() {
  return renderAmis(
    {
      type: "page",
      body: {
        type: "form",
        body: [
          {
            type: "radios",
            name: "foo",
            label: false,
            options: [
              {
                label: "类型1",
                value: 1,
              },
              {
                label: "类型2",
                value: 2,
              },
            ],
          },
          {
            type: "input-text",
            name: "text1",
            label: false,
            placeholder: "选中 类型1 时可见",
            visibleOn: "${foo == 1}",
          },
          {
            type: "input-text",
            name: "text2",
            label: false,
            placeholder: "选中 类型2 时不可点",
            disabledOn: "${foo == 2}",
          },
        ],
      },
    },
    {},
    {
      theme: "antd",
    },
  );
}
