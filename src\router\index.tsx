import { useEffect, useState } from "react";
import { useLocation, useRoutes } from "react-router";
import { useSelector, useDispatch } from "react-redux";
import { useRouteConfig } from "./useRouteConfig";
import { updatePages } from "../store/pages";
import { sleep } from "../utils/sleep";
import menuConfig from "./config";

export function PageRouter() {
  const location = useLocation();

  const { staticRoutes, allRoutes, dynamicRoutes } = useRouteConfig();

  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (staticRoutes.map((el) => el.path).includes(location.pathname) === false && dynamicRoutes.length === 0) {
      // 从接口获取菜单
      setLoading(true);
      fetchRemoteRoutes()
        .then((res) => {
          dispatch(updatePages(res));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [dispatch, dynamicRoutes, location, staticRoutes]);

  const Router = useRoutes(allRoutes);
  return loading ? <div>菜单获取中...</div> : Router;
}

// 获取远程菜单
async function fetchRemoteRoutes() {
  await sleep(100);
  // console.log(23234, menuConfig);
  return menuConfig;
}
