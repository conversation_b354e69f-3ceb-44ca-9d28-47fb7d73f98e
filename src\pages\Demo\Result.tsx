import { Col, Divider, Flex, Row, Space, Statistic, Table, Typography } from "antd";
const { Text, Title } = Typography;
const dataSource = [
  {
    key: "1",
    c1: "SQL注入",
    c2: "8",
    c3: "高危",
    c4: `1. 使用参数化查询或预编译语句
2. 对输入参数进行白名单验证
3. 实施最小权限原则`,
  },
  {
    key: "2",
    c1: "文件上传漏洞",
    c2: "6",
    c3: "中危",
    c4: `1. 限制允许上传的文件类型
2. 验证文件内容与扩展名是否匹配
3. 将上传文件存储在非web可访问目录`,
  },
];

const columns = [
  {
    title: "漏洞名称",
    dataIndex: "c1",
    key: "c1",
    width: 200,
  },
  {
    title: "可信度",
    dataIndex: "c2",
    key: "c2",
    width: 100,
  },
  {
    title: "危险程度",
    dataIndex: "c3",
    key: "c3",
    width: 100,
  },
  {
    title: "修复建议",
    dataIndex: "c4",
    key: "c4",
  },
];

export default function Result() {
  return (
    <Flex style={{ width: 800 }} vertical>
      <Text>漏洞分析结果：</Text>
      <br />
      <Space>
        <div>
          <Text type="danger">高危漏洞</Text>
          <Text>20</Text>
        </div>
        <Divider type="vertical" />
        <div>
          <Text type="warning">中危漏洞</Text>
          <Text>30</Text>
        </div>
        <Divider type="vertical" />
        <div>
          <Text>低危漏洞</Text>
          <Text>5</Text>
        </div>
      </Space>
      <br />

      <Table dataSource={dataSource} columns={columns} pagination={false} />
    </Flex>
  );
}
