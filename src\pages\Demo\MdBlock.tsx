import markdownIt from "markdown-it";
import ReactDOMServer from "react-dom/server";

import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
// import { tomorrow } from "react-syntax-highlighter/dist/cjs/styles/prism";

const md = markdownIt({
  html: true,
  breaks: true,
  highlight: (str, lang) => {
    const html = ReactDOMServer.renderToStaticMarkup(
      <SyntaxHighlighter language={lang || "java"}>{str}</SyntaxHighlighter>,
    );
    return html;
  },
});

export default function MdBlock({ code }: { code: string }) {
  return <div dangerouslySetInnerHTML={{ __html: md.render(code) }} />;
}
