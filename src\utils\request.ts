import { message } from "antd";
import axios, { type AxiosInstance, type AxiosResponse, type InternalAxiosRequestConfig } from "axios";

// 定义响应数据的基本结构 (可以根据后端API调整)
interface ApiResponse<T = any> {
  status: number;
  message: string;
  data: T;
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // API 的 base_url, 从环境变量读取或默认 /api
  timeout: 1000000, // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么
    // 例如，从 localStorage 获取 token
    const token = localStorage.getItem("authToken");
    if (token) {
      // 让每个请求携带自定义 token 请根据实际情况自行修改
      config.headers.Authorization = `Bearer ${token}`;
    }
    // 您可以在这里添加其他通用的请求头，例如 'Content-Type'
    // if (!config.headers['Content-Type']) {
    //   config.headers['Content-Type'] = 'application/json';
    // }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    console.error("Request Error:", error); // for debug
    return Promise.reject(error);
  },
);

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果您想获取诸如 http 头或状态之类的 http 信息
   * 请返回 response => response
   */

  /**
   * 通过自定义代码确定请求状态
   * 这里只是一个例子
   * 您也可以通过HTTP状态代码判断状态
   */
  (response: any) => {
    if (response.status != 200) {
      console.error(response);
      return Promise.reject(new Error(response.message || "Error"));
    }
    const { status, msg, data } = response.data;
    if (status != 200) {
      return Promise.reject(new Error(msg || "Error"));
    }
    return data;
    // 通常，如果后端返回的业务 code 不是成功状态 (例如，非 0 或非 200)，则判断为错误。
    // 这个判断条件需要根据您的后端API规范来调整。
    // 假设 code 为 0 或 200 表示成功
    // if (res.code !== 0 && res.code !== 200) {
    // 这里可以根据不同的 code 执行不同的操作
    // 例如：
    // if (res.code === 401) {
    //   // Token 过期或无效，处理重新登录的逻辑
    //   console.error('认证失败或Token已过期:', res.message);
    //   // MessageBox.confirm('您已登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
    //   //   confirmButtonText: '重新登录',
    //   //   cancelButtonText: '取消',
    //   //   type: 'warning'
    //   // }).then(() => {
    //   //   store.dispatch('user/resetToken').then(() => {
    //   //     location.reload(); // 为了重新实例化vue-router对象 避免bug
    //   //   });
    //   // });
    //   // return Promise.reject(new Error(res.message || 'Error'));
    // } else if (res.code === 403) {
    //   console.error('禁止访问:', res.message);
    // } else {
    //   // 其他业务错误
    //   console.error('业务错误:', res.message);
    // }
    // 默认情况下，如果业务 code 不符合预期，也抛出错误，让调用方 catch
    // console.error("API Error:", res.message || "Unknown error");
    // return Promise.reject(new Error(res.message || "Error"));
    // } else {
    // 如果业务 code 表示成功，则直接返回 data 部分
    // return res.data; // 或者返回 res，如果调用方需要完整的 ApiResponse
    // }
  },
  (error) => {
    // console.error("Response Error:", error.message); // for debug
    // 处理 HTTP 网络错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 处理未授权错误，例如跳转到登录页
          console.error("未授权 (401)，请检查Token或登录状态。");
          // window.location.href = '/login'; // 示例：跳转到登录页
          break;
        case 403:
          console.error("禁止访问 (403)。");
          break;
        case 404:
          console.error("请求资源未找到 (404)。");
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          console.error(`服务器错误 (${error.response.status})。`);
          break;
        default:
          console.error(`HTTP错误 (${error.response.status}): ${error.message}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error("网络请求超时或服务器未响应:", error.request);
    } else {
      // 发送请求时出了点问题
      console.error("请求配置错误:", error.message);
    }
    return Promise.reject(error);
  },
);

// 导出 axios 实例
export default service;

// 您可以进一步封装 GET, POST, PUT, DELETE 等方法，使其更易用
// 例如:
// export function get<T = any>(url: string, params?: object): Promise<T> {
//   return service.get<T, T>(url, { params });
// }

// export function post<T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
//   return service.post<T, T>(url, data, config);
// }
