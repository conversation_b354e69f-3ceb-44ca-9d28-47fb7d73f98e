import { Result } from "antd";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { Layout } from "../layout";
import NotFound from "../pages/NotFound";
import omit from "lodash-es/omit";
import { staticRoutes } from "./staticRoutes";

const renderStatusNotExist = () => <Result subTitle="页面文件没找到" />;
// 导入所有模块
const pages = import.meta.glob(`../pages/**/*.tsx`, { eager: true });

export function useRouteConfig() {
  // 远程菜单
  const remotePages: any = useSelector<any>((state) => state.pages.list);

  // 转换成路由
  const dynamicRoutes = useMemo(() => {
    const allPages = flattenMenuTree(remotePages);
    return allPages
      .filter((el) => {
        // 过滤掉没有component的
        return !!el.component;
      })
      .map((el) => {
        // console.log(2333, el);
        const Component = (pages[("../pages" + el.component) as string] as any)?.default || renderStatusNotExist;
        return {
          path: el.path,
          element: <Component />,
        };
      });
  }, [remotePages]);

  const allRoutes = useMemo(() => {
    return [
      ...staticRoutes,
      {
        path: "/",
        element: <Layout />,
        children: [...dynamicRoutes, { path: "*", element: <NotFound /> }],
      },
      {
        path: "*",
        element: <NotFound />,
      },
    ];
  }, [dynamicRoutes]);

  return { allRoutes, staticRoutes, dynamicRoutes };
}

/**
 * 扁平化菜单树
 * @param menuTree
 * @returns
 */
function flattenMenuTree(menuTree: any[]): any[] {
  let flattenedArray: any[] = [];
  function traverseTree(node: any) {
    flattenedArray.push(node);
    if (Array.isArray(node.children) && node.children) {
      node.children.forEach((child) => traverseTree(child));
    }
  }
  menuTree.forEach((rootNode) => traverseTree(rootNode));
  flattenedArray = flattenedArray.map((el) => ({ ...omit(el, ["children"]) }));
  return flattenedArray;
}
