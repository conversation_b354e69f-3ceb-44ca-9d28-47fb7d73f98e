import React from "react";
import {
  Steps,
  Card,
  Typography,
  Divider,
  Collapse,
  Tag,
  Space,
  Alert,
} from "antd";
import {
  RobotOutlined,
  CheckCircleOutlined,
  FileSearchOutlined,
  DownOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";

const { Step } = Steps;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

// 步骤数据结构
const steps = [
  {
    title: "API接口梳理",
    time: "用时1分20秒",
    tasks: [
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "梳理函数调用接口",
        content: (
          <Paragraph style={{ margin: 0 }}>
            共计找到接口 <Tag color="blue">63个</Tag>
          </Paragraph>
        ),
      },
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "接口分类",
        token: 8,
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => (
              <DownOutlined rotate={isActive ? 180 : 0} />
            )}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  请将查询到的接口按污点源（Source）、传播点（Trans）、污点汇聚点（Sink）类型进行分类
                </Text>
              }
              key="1"
              style={{ backgroundColor: "transparent" }}
            >
              <div style={{ marginLeft: 24 }}>
                <Paragraph style={{ margin: 0 }}>
                  <Text strong>分类完成。</Text>
                </Paragraph>
                <Space size="middle">
                  <Tag color="red">污点源（Source）5个</Tag>
                  <Tag color="orange">传播点（Trans）12个</Tag>
                  <Tag color="green">污点汇聚点（Sink）6个</Tag>
                </Space>
              </div>
            </Panel>
          </Collapse>
        ),
      },
      {
        icon: <FileSearchOutlined style={{ color: "#faad14" }} />,
        title: "分析Source-Trans-Sink调用过程",
        content: (
          <Paragraph style={{ margin: 0 }}>
            查找完成。找到调用成功的调用链 <Tag color="blue">12个</Tag>
          </Paragraph>
        ),
      },
    ],
  },
  {
    title: "漏洞检测",
    time: "用时1分20秒",
    token: 16,
    tasks: [
      {
        icon: <RobotOutlined style={{ color: "#faad14" }} />,
        title: "漏洞检测",
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => (
              <DownOutlined rotate={isActive ? 180 : 0} />
            )}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  你是一名安全专家。分析这个 Java
                  项目中的数据流路径，并预测其是否包含代码注入漏洞（CWE-094）或相关漏洞。请注意可疑的错误消息有时可能...
                </Text>
              }
              key="2"
              style={{ backgroundColor: "transparent" }}
            >
              <Alert
                type="error"
                showIcon
                icon={<ExclamationCircleOutlined />}
                message={
                  <span>
                    共计发现{" "}
                    <Text strong type="danger">
                      2
                    </Text>{" "}
                    个漏洞！
                  </span>
                }
                style={{ marginLeft: 24 }}
              />
            </Panel>
          </Collapse>
        ),
      },
    ],
  },
  {
    title: "漏洞研判",
    time: "用时20秒",
    tasks: [
      {
        icon: <RobotOutlined style={{ color: "#faad14" }} />,
        title: "自动生成POC",
        token: 10,
        content: (
          <Collapse
            bordered={false}
            expandIcon={({ isActive }) => (
              <DownOutlined rotate={isActive ? 180 : 0} />
            )}
            style={{ marginTop: 8 }}
          >
            <Panel
              header={
                <Text type="secondary">
                  你是一个专门研究RCE漏洞的验证和利用的研究员，你精通Nuclei漏洞验证框架。你要根据下面的漏洞描述，生成nuclei可用的poc...
                </Text>
              }
              key="3"
              style={{ backgroundColor: "transparent" }}
            >
              <Paragraph style={{ marginLeft: 24, marginBottom: 0 }}>
                执行完成。一共生成 <Tag color="blue">2个POC</Tag>
              </Paragraph>
            </Panel>
          </Collapse>
        ),
      },
      {
        icon: <CheckCircleOutlined style={{ color: "#52c41a" }} />,
        title: "POC验证",
        content: (
          <Paragraph style={{ margin: 0 }}>
            验证完成。成功 <Tag color="green">2个</Tag>，失败{" "}
            <Tag color="red">0个</Tag>
          </Paragraph>
        ),
      },
    ],
  },
];

interface CodeProcessStepsProps {
  step?: 1 | 2 | 3 | 4 | 5;
}

const CodeProcessSteps: React.FC<CodeProcessStepsProps> = ({ step = 1 }) => {
  return (
    <div
      style={{
        maxWidth: 800,
        margin: "0 auto",
        padding: 24,
        background: "#fafbfc",
        borderRadius: 24,
      }}
    >
      {/* 步骤条 */}
      {/* <Steps current={2} style={{ marginBottom: 32 }}>
        <Step title="第三步：API接口梳理" />
        <Step title="第四步：漏洞检测" />
        <Step title="第五步：漏洞研判" />
      </Steps> */}

      {/* 步骤内容卡片 */}
      {(() => {
        let showSteps: typeof steps = [];
        if (step === 1 || step === 5) {
          showSteps = steps;
        } else if (step === 2) {
          showSteps = [steps[0]];
        } else if (step === 3) {
          showSteps = steps.slice(0, 2);
        } else if (step === 4) {
          showSteps = steps.slice(0, 3);
        }
        return showSteps.map((item, idx) => (
          <Card
            key={item.title}
            style={{ marginBottom: 32, boxShadow: "0 2px 12px #f0f1f2" }}
            bordered={false}
            bodyStyle={{ padding: 24 }}
          >
            <Space direction="vertical" size="large" style={{ width: "100%" }}>
              <Space
                align="center"
                style={{ justifyContent: "space-between", width: "100%" }}
              >
                <Text
                  strong
                  style={{ fontSize: 18 }}
                >{`第${idx + 3}步：${item.title}`}</Text>
                <Space>
                  <ClockCircleOutlined style={{ color: "#1890ff" }} />
                  <Text type="secondary">{item.time}</Text>
                  {item.token && (
                    <Tag color="purple">消耗Token{item.token}个</Tag>
                  )}
                </Space>
              </Space>
              <Divider style={{ margin: "8px 0" }} />
              <Space
                direction="vertical"
                style={{ width: "100%" }}
                size="middle"
              >
                {item.tasks.map((task, tIdx) => (
                  <Card
                    key={task.title}
                    type="inner"
                    style={{ background: "#f6f8fa", borderRadius: 12 }}
                    bodyStyle={{ padding: 16 }}
                  >
                    <Space direction="vertical" style={{ width: "100%" }}>
                      <Space
                        align="center"
                        style={{
                          justifyContent: "space-between",
                          width: "100%",
                        }}
                      >
                        <Space>
                          {task.icon}
                          <Text strong>{`任务${tIdx + 1}：${task.title}`}</Text>
                        </Space>
                        {task.token && (
                          <Tag color="purple">消耗Token{task.token}个</Tag>
                        )}
                      </Space>
                      <div>{task.content}</div>
                    </Space>
                  </Card>
                ))}
              </Space>
            </Space>
          </Card>
        ));
      })()}
    </div>
  );
};

export default CodeProcessSteps;
