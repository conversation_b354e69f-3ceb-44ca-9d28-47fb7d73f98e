.custom-scrollbar {
  /* 滚动条整体样式 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.show-detail {
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
}