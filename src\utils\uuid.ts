const appName = "-";

export function UUID() {
  if (typeof crypto === "object" && typeof crypto.randomUUID === "function") {
    return appName + crypto.randomUUID();
  } else {
    // 备用方法，使用时间戳和随机数组合
    let d = new Date().getTime();
    const uuid = "xxxxxxxx-4xxx-yxxx-zxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === "x" ? r : (r & 0x3) | 0x8).toString(16);
      },
    );
    return appName + uuid;
  }
}
