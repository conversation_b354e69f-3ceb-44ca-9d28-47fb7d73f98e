import { Bubble } from "@ant-design/x";
import type { ReactNode } from "react";

export default function MyBubble({
  children,
  style = {},
  noTyping,
}: {
  children: ReactNode;
  style?: React.CSSProperties;
  noTyping?: boolean;
}) {
  return (
    <Bubble
      typing={!noTyping}
      content={children}
      styles={{
        content: {
          backgroundColor: "transparent",
          padding: 0,
          // height: ,
          minHeight: "auto",
          // outline: "solid 1px red",
          ...style,
        },
      }}
    />
  );
}
