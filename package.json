{"name": "project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.10", "@ant-design/x": "^1.4.0", "@reduxjs/toolkit": "^2.8.2", "amis": "^6.12.0", "amis-ui": "^6.12.0", "antd": "^5.25.1", "axios": "^1.9.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "react-router": "^7.6.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "less": "^4.3.0", "prettier": "3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}